namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelBaseForCalculationAgeBasedMinimumConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelBaseForCalculationAgeBasedMinimum>
{
    public override void Configure(EntityTypeBuilder<ModelBaseForCalculationAgeBasedMinimum> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelGrondslagMinimum", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, x.BaseForCalculationId, x.Age, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.BaseForCalculationId).HasColumnName("GrondslagID");
        builder.Property(x => x.Age).HasColumnName("GrondslagMinimumID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");
        builder.Property(x => x.Minimum).HasColumnName("Bedrag");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
    }
}