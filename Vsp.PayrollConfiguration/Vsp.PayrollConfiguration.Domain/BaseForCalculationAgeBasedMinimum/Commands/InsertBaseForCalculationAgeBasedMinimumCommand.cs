using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Commands;

public class InsertBaseForCalculationAgeBasedMinimumCommand(
    IInsertCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum> query,
    IServiceProvider serviceProvider)
    : InsertInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPostModel, BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum, ModelBaseForCalculationAgeBasedMinimum>(dependencies, query, serviceProvider)
{
    protected override bool AddFutureYears => true;
}