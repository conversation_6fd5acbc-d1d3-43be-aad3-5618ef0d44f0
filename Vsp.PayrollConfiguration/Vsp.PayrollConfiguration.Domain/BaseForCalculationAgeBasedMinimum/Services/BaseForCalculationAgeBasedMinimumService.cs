using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Services;

public class BaseForCalculationAgeBasedMinimumService(
    IMapper mapper,
    IGetInheritanceEntityQuery<BaseForCalculationAgeBasedMinimumModel,
        Repository.Entities.BaseForCalculationAgeBasedMinimum> getQuery,
    IInsertInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPostModel, BaseForCalculationAgeBasedMinimumModel,
        Repository.Entities.BaseForCalculationAgeBasedMinimum, ModelBaseForCalculationAgeBasedMinimum> insertCommand,
    IPatchInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPatchModel, BaseForCalculationAgeBasedMinimumModel,
        Repository.Entities.BaseForCalculationAgeBasedMinimum, ModelBaseForCalculationAgeBasedMinimum> patchCommand,
    IDeleteInheritanceEntityCommand<ModelBaseForCalculationAgeBasedMinimum> deleteCommand
)
{
    private readonly
        IGetInheritanceEntityQuery<BaseForCalculationAgeBasedMinimumModel,
            Repository.Entities.BaseForCalculationAgeBasedMinimum> getQuery = getQuery;

    private readonly
        IInsertInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPostModel,
            BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum,
            ModelBaseForCalculationAgeBasedMinimum> insertCommand = insertCommand;

    private readonly
        IPatchInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPatchModel,
            BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum,
            ModelBaseForCalculationAgeBasedMinimum> patchCommand = patchCommand;

    private readonly IDeleteInheritanceEntityCommand<ModelBaseForCalculationAgeBasedMinimum> deleteCommand =
        deleteCommand;
}