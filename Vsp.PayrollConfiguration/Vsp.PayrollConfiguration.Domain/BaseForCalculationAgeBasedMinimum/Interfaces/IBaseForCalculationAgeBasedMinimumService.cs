using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Interfaces;

public interface IBaseForCalculationAgeBasedMinimumService
{
    Task<IListOperationResult<BaseForCalculationAgeBasedMinimumModel>>
        GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync(Guid baseForCalculationId);

    Task<IOperationResult<BaseForCalculationAgeBasedMinimumModel>>
        PostBaseForCalculationAgeBasedMinimumByBaseForCalculationIdAsync(Guid baseForCalculationId,
            BaseForCalculationAgeBasedMinimumPostModel postModel);

    Task<IOperationResult<BaseForCalculationAgeBasedMinimumModel>>
        PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync(
            Guid baseForCalculationAgeBasedMinimumId, BaseForCalculationAgeBasedMinimumPatchModel patchModel);

    Task<IOperationResult<NoResult>> DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync(
        Guid baseForCalculationAgeBasedMinimumId);
}