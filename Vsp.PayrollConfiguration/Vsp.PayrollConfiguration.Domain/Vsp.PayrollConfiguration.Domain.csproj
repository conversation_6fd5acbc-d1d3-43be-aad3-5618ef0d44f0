<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AnalysisLevel>latest</AnalysisLevel>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\Vsp.PayrollConfiguration.Infrastructure\Vsp.PayrollConfiguration.Infrastructure.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" Version="9.103.9.1" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="BaseForCalculationAgeBasedMinimum\Interfaces\" />
    <Folder Include="BaseForCalculationAgeBasedMinimum\Validators\" />
  </ItemGroup>
</Project>
